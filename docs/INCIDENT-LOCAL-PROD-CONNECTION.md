# Incident Report: Local Development Environment Connected to Production Database

## Date of Incident
Friday, [Date]

## Summary
A developer's local development environment was inadvertently configured to connect to the production database, causing their local machine to act as a production server and process production workloads. This resulted in failed video processing tasks and missing assets (videos, thumbnails, etc.) due to the local environment attempting to execute production jobs without the proper infrastructure.

---

## What Happened

### The Configuration Change
A developer (<PERSON>) modified their local `.env` file to point to production database credentials:
- Changed `DB_HOST` to point to a production database server
- Likely also updated `DB_USERNAME` and `DB_PASSWORD` to production credentials
- The developer's IP address was already whitelisted in AWS security groups for both:
  - Application server security groups
  - Database security groups

### Why This Caused Production Impact

#### 1. **<PERSON>vel Scheduler Running Locally**
The ThankView application uses Docker for local development, and the Docker container automatically runs a cron job every minute:

<augment_code_snippet path="docker/cron/cron-default" mode="EXCERPT">
````
* * * * * root /usr/local/bin/php /var/www/html/artisan schedule:run >/var/log/stdout.log 2>/var/log/stderr.log
````
</augment_code_snippet>

This cron job executes <PERSON><PERSON>'s scheduler, which checks `app/Console/Kernel.php` to determine which scheduled commands should run.

#### 2. **Default Environment Behavior**
When `APP_ENV` is not set to `production` or `staging`, the scheduler falls into the `else` block and runs several critical commands:

<augment_code_snippet path="app/Console/Kernel.php" mode="EXCERPT">
````php
} else {
    $schedule->command('assign:sends')->withoutOverlapping(10);
    $schedule->command('assign:video')->withoutOverlapping(10);
    $schedule->command('consume:personal-video-status')->everyFiveSeconds()->withoutOverlapping();
    
    $schedule->command('captions:process')->withoutOverlapping();
    $schedule->command('captions:processAi')->withoutOverlapping(30);
    // ... more commands
}
````
</augment_code_snippet>

#### 3. **Video Processing Assignment**
The `assign:video` command queries the production database for videos that need processing and creates `video_process_dry_runs` records with the local machine's hostname:

<augment_code_snippet path="app/VideoProcessRun.php" mode="EXCERPT">
````php
public function start()
{
    $this->started_at = Carbon::now();
    $this->host = gethostname();  // Records local machine hostname
    $this->count++;
    $this->save();
}
````
</augment_code_snippet>

#### 4. **Why Jobs Failed**
The local development environment:
- ✅ **Could** query the production database
- ✅ **Could** create job records in the database
- ✅ **Could** dispatch jobs to SQS queues
- ❌ **Could NOT** access production S3 buckets properly
- ❌ **Could NOT** access production video processing infrastructure
- ❌ **Could NOT** execute video processing tasks successfully
- ❌ **Did NOT** have the proper AWS credentials/permissions

This resulted in:
- Videos marked as "executing" but never completed
- Missing video files
- Missing thumbnails
- Failed email sends
- Incomplete video processing jobs

---

## Root Cause Analysis

### Primary Causes
1. **No Environment Guards**: The scheduler runs on ANY environment that isn't explicitly `production` or `staging`
2. **Automatic Cron Execution**: Docker container automatically starts cron, which runs the scheduler every minute
3. **Network Access**: Developer's IP was whitelisted in production security groups
4. **No Hostname Validation**: The application doesn't validate that the hostname matches expected production servers

### Contributing Factors
1. **Developer workflow**: Connecting to production database for debugging/testing purposes
2. **Security group configuration**: Broad IP whitelisting for developer access
3. **Lack of safeguards**: No warnings or checks when connecting to production databases from non-production environments

---

## Impact

### Technical Impact
- Production video processing jobs were assigned to a local development machine
- Jobs failed due to missing infrastructure and permissions
- Database records show the local machine hostname in `video_process_dry_runs.host` field
- Videos, thumbnails, and other assets were not generated
- Email sends may have been affected

### Business Impact
- Customer videos were not processed
- Email campaigns may have been delayed or failed
- Customer experience was degraded

---

## Prevention Strategies

### 1. **Add Environment Guards to Scheduler** (RECOMMENDED - HIGH PRIORITY)

Modify `app/Console/Kernel.php` to only run scheduled tasks in known production/staging environments:

```php
protected function schedule(Schedule $schedule)
{
    $env = env('APP_ENV');
    $role = env('APP_ROLE');
    $runsJobs = env('RUNS_JOBS');

    // SAFETY CHECK: Only run scheduled tasks in production or staging
    if (!in_array($env, ['production', 'staging'])) {
        // Log that scheduler is disabled for this environment
        \Log::info('Scheduler disabled for environment: ' . $env);
        return;
    }

    if ($env == 'production') {
        // ... existing production schedules
    } elseif ($env == 'staging') {
        // ... existing staging schedules
    }
    
    // Remove the 'else' block that runs tasks for all other environments
}
```

**Benefits:**
- Prevents local environments from running production tasks
- Simple, one-line check
- No impact on production or staging
- Immediate protection

### 2. **Add Hostname Validation** (RECOMMENDED - MEDIUM PRIORITY)

Add validation to ensure only known production hostnames can process production jobs:

```php
// In app/VideoProcessRun.php or a new middleware
public function start()
{
    $hostname = gethostname();
    $env = env('APP_ENV');
    
    // Validate hostname matches expected pattern for this environment
    if ($env === 'production') {
        $validPatterns = [
            '/^thankview-prod-.*/',
            '/^ip-.*\.ec2\.internal$/',
        ];
        
        $isValid = false;
        foreach ($validPatterns as $pattern) {
            if (preg_match($pattern, $hostname)) {
                $isValid = true;
                break;
            }
        }
        
        if (!$isValid) {
            throw new \Exception("Invalid hostname '$hostname' for production environment");
        }
    }
    
    $this->started_at = Carbon::now();
    $this->host = $hostname;
    $this->count++;
    $this->save();
}
```

### 3. **Disable Cron in Local Development** (RECOMMENDED - HIGH PRIORITY)

Modify the Dockerfile to not start cron by default in local development:

```dockerfile
# Instead of:
CMD ( cron -f -l 8 & ) && apache2-foreground

# Use:
CMD if [ "$APP_ENV" = "production" ] || [ "$APP_ENV" = "staging" ]; then \
      ( cron -f -l 8 & ) && apache2-foreground; \
    else \
      apache2-foreground; \
    fi
```

Or create a separate docker-compose override for local development that doesn't include cron.

### 4. **Database Connection Validation** (RECOMMENDED - MEDIUM PRIORITY)

Add a check in `config/database.php` or a service provider to warn/prevent production database connections from non-production environments:

```php
// In AppServiceProvider.php boot() method
public function boot()
{
    if (app()->environment('local', 'testing', 'development')) {
        $dbHost = config('database.connections.mysql.host');
        
        // Check if connecting to production database
        $productionHosts = [
            'thankview-prod-db.cluster-xxxxx.us-east-1.rds.amazonaws.com',
            // Add other production DB hosts
        ];
        
        if (in_array($dbHost, $productionHosts)) {
            // Option 1: Throw exception (strict)
            throw new \Exception(
                "SAFETY CHECK FAILED: Cannot connect to production database from " . 
                app()->environment() . " environment"
            );
            
            // Option 2: Log warning and continue (permissive)
            \Log::critical(
                "WARNING: Local environment connected to production database!",
                ['host' => $dbHost, 'env' => app()->environment()]
            );
        }
    }
}
```

### 5. **Network Security Improvements** (OPTIONAL - LONG TERM)

- **Reduce IP Whitelisting**: Limit developer IP whitelisting to read-only database replicas
- **VPN Requirement**: Require VPN connection for production database access
- **Bastion Host**: Use a bastion host for production database access instead of direct connections
- **Separate Read Replicas**: Provide read-only database replicas for developers to use for debugging

### 6. **Developer Education** (REQUIRED - IMMEDIATE)

- **Document the risk**: Create clear documentation about the dangers of connecting local environments to production
- **Provide alternatives**: Document how to safely debug production issues (read replicas, log analysis, etc.)
- **Code review**: Include environment configuration checks in code review process
- **Onboarding**: Add this incident to developer onboarding materials

### 7. **Monitoring and Alerting** (RECOMMENDED - MEDIUM PRIORITY)

Add monitoring to detect unusual hostnames in production:

```sql
-- Query to find unexpected hostnames in video_process_dry_runs
SELECT DISTINCT host, COUNT(*) as count
FROM video_process_dry_runs
WHERE created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)
  AND host NOT LIKE 'thankview-prod-%'
  AND host NOT LIKE 'ip-%'
GROUP BY host
ORDER BY count DESC;
```

Create a Datadog monitor or CloudWatch alarm that alerts when:
- `video_process_dry_runs` records are created with unexpected hostnames
- Jobs are failing at an unusual rate
- Jobs are being processed by unknown hosts

---

## Recommended Implementation Priority

### Immediate (This Week)
1. ✅ **Add environment guard to scheduler** - Prevents recurrence immediately
2. ✅ **Disable cron in local Docker** - Prevents local scheduler execution
3. ✅ **Document incident and prevention** - Educate team

### Short Term (Next Sprint)
4. **Add database connection validation** - Warns developers before they cause issues
5. **Add hostname validation** - Additional safety layer
6. **Set up monitoring** - Detect issues quickly

### Long Term (Next Quarter)
7. **Review network security** - Reduce attack surface
8. **Implement read replicas** - Provide safe debugging alternatives

---

## Testing the Fix

After implementing the environment guard, test that:

1. **Local environment doesn't run scheduler:**
   ```bash
   # In local Docker container
   dcl php artisan schedule:list
   # Should show no scheduled tasks or log that scheduler is disabled
   ```

2. **Production still works:**
   ```bash
   # On production server
   php artisan schedule:list
   # Should show all expected scheduled tasks
   ```

3. **Staging still works:**
   ```bash
   # On staging server
   php artisan schedule:list
   # Should show all expected scheduled tasks
   ```

---

## Conclusion

This incident was caused by a combination of:
- Developer connecting local environment to production database
- Automatic cron execution in Docker
- Lack of environment guards in the scheduler
- Network access allowing the connection

The recommended fixes are straightforward and will prevent this from happening again while maintaining developer productivity and production reliability.

**Most Important Fix:** Add the environment guard to `app/Console/Kernel.php` to prevent the scheduler from running in non-production/staging environments. This single change would have prevented this entire incident.

